<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html languagee="ja">
<head>
<title>����礻����⥸�塼��</title>
<style>
.outer { background-color: #808080; }
td,th { padding: 4px; background: #e8e8e8;}
.head th  { padding: 4px; background: #d0d0d0; }
.bnf { color: #008000; }
.note { font-size: 80%; color: #800000; padding: 0.5em;}
h1, h2, h3, h4, h5, h6 { color: #000040; }
pre { color: #600000; }
</style>
</head>
<body>
  <h4>����礻����⥸�塼��</h4>
  <ul>
  <li><a href="#intro">����</a></li>
  <li><a href="#form">�ե�����λ���</a></li>
  <li><a href="#custom">��������ե�����</a></li>
  <li><a href="#config">��������</a></li>
  <li><a href="#attr">���ץ�����ѿ�</a></li>
  <li><a href="#tips">�Ȥ������</a></li>
  <li><a href="#changes">�ѹ�����</a></li>
  </ul>

  <hr>

  <a name="intro"></a><h5>����</h5>

  <p>�֤���礻����פϡ��䤤��碌�ե����� �Τ褦�ʤ�Τ�Ȥ������
    �⥸�塼��Ǥ��롣�⥸�塼��ϡ����Τ褦����ħ����äƤ��롣</p>

  <ul>
    <li>�������ޥ�����ǽ����礻�ե�����</li>
    <li>��礻�Υǡ����١�����¸</li>
    <li>��礻�б���ǽ</li>
    <li>�б��ʼ���ɾ��</li>
  </ul>

  <p>����Υ᡼��ե�����Ȱۤʤ�Τϡ���礻��ǡ����١�������¸����
    ��礻�б��ޤǻٱ礹�����Ǥ��롣</p>

  <a name="form"></a><h5>�ե�����λ���</h5>

  �ե�����κ����ϡ�������˥塼�Ρ֥ե�����κ����פǹԤ���

  �֥ե����������׹��ܤ����Ǥ��롣����ν񼰤ϼ��̤ꡣ
  <blockquote class="bnf">
      �� ::= ����̾["*"][=ɽ����٥�][,������][,°������...][,����...]<p />
      ������ ::= {text|checkbox|radio|textarea|select|const|hidden|mail|file}<br />
      ���� ::= [�� ["+"] "="] ��٥�<br />
      °������ ::= °��̾ "=" ��<br />
      °��̾ ::= {size|rows|maxlength|cols|prop|check}
  </blockquote>
  <ul>
    <li>"*" ��ɬ�ܹ��ܤ�ɽ����</li>
    <li>"#" �ǥե���������ɲä��륳���Ȥ򵭽ҤǤ��롣</li>
    <li>"," �϶��ڵ���Τ��ᡢ������ǻȤ���� "\" �������֤��ƥ��������פ��롣</li>
    <li>�������ͤ���ά���줿��硢��٥��Ʊ���ˤʤ롣</li>
    <li>"+" ������� (checkbox, radio, select) ��������֤�ؼ����롣</li>
    <li>�ƥ������ΰ� (text, textarea) �ǤΡ�����¾�ΰ�����ɸ���ͤȤʤ롣</li>
    <li>ͭ����°�������Ǥ˰ۤʤ� (text: "size=n", textarea: "cols=n" "rows=n")��</li>
    <li>check°���ϸ���������ꤹ�� (require: ɬ�ܡ�����̾�� "*" ��Ʊ����"num": ���͡�"mail": �᡼��񼰡�����¾: ����ɽ���ǻ���)</li>
    <li>checkbox, radio �ΰ����Ȥ��ơ��� "*" ����ꤹ��ȡ�����¾��ƥ��������Ϥǻ���Ǥ��롣</li>
    <li>mail �ϡ����̤ʥե�����ɤȤ��ư����롣��ưŪ�˳�ǧ�Ѥι��ܤ��������졢���׳�ǧ���Ԥ��롣�ե�������ǰ��٤������ѤǤ��롣</li>
    <li>file �ϡ�ź�եե�����Υ��åץ��ɤ���ꤹ�롣</li>
    <li>
      �����ͤ�ʸ����Ȥ��ơ�<tt>{X_̾��}</tt> �Τ褦���ѿ�����ꤹ��С�
      ������桼���ξ�����ͤȤ���Ÿ�����졢�����Ȥξ��϶�ʸ����ˤʤ롣
      ����Ū�ˤ� {X_UNAME}, {X_NAME}, {X_EMAIL}, {X_BIO} �ʤɤ�����Ǥ��롣
    </li>
    <li>
      ���̾������ʸ�פˤ����ƹ���̾�� "-" �ǻϤ᤿��硢���ι���
      �����ιԤ�Ʊ���Ԥ�ɽ�����롣

      ����ϥե������ѥƥ�ץ졼�� (ccenter_form.html,
      ccenter_confirm.html) �Ǽ¸�����Ƥ����ص���ε�ǽ�Ǥ��롣
    </li>
  </ul>

  <h6>��:</h6>

  <blockquote>
  <form>
    <table border="0" cellspacing="1" cellpadding="4" class="outer">
      <tr class="head"><th>ɽ��</th><th>�ե��������</th><th>����</th></tr>
      <tr class="even"><td>̾��*</td><td>̾��* <input name='samp1' /></td><td>����̾�������� * ���դ����ɬ�ܹ��ܤˤʤ롣�����פ��ά������� text��</td></tr>
      <tr class="odd"><td>̾��*,size=5</td><td>̾��* <input name='samp2' size=5 /></td><td>°�� (�礭��) ����ꤹ�롣</td></tr>
      <tr class="even"><td>̾��*,size=10,������,#������ʸ</td><td>̾��* <input name='samp3' size='10' value='������'/> ������ʸ</td><td>��������Ƭ�� "#" �ˤ��������ʸ�Ȥ���ɽ������</td></tr>
      <tr class="odd"><td>̾��,size=10,check=reqire,{X_NAME},#������ʸ</td><td>̾�� <input name='samp3' size='10' value='��̾'/> ������ʸ</td><td>ɬ�ܸ����� require �Ȥ��ƻ���Ǥ��롣�����ͤ� <tt>{X_NAME}</tt>�ϥ桼���������̾�����ꤷ�Ƥ����Τˤʤ롣</td></tr>
      <tr class="even"><td>����,radio,����1+,����2&lt;br/&gt;,*=����¾</td><td>���� <input type='radio' name='samp4' value='1' checked /> ����1 &nbsp; <input type='radio' name='samp4' value='2' /> ����2<br/> <input type='radio' name='samp4' value='*' /> ����¾ <input name='etc4' size='8'/> </td><td>�����ˤ� HTML ������ޤ�뤳�Ȥ��Ǥ��뤬�ͤȤ��Ƥ�̵�����ͤ� "*" �˻��ꤹ��Ȥ���¾��ƥ����Ȼ���Ǥ��롣</td></tr>
      <tr class="odd"><td>����,checkbox,����1+,����2,����3</td><td>���� <input type='checkbox' name='samp5_1' checked /> ����1 &nbsp; <input type='checkbox' name='samp5_2' /> ����2 &nbsp; <input type='checkbox' name='samp5_3' /> ����3 &nbsp; </td><td>�����ͤΥǥե��������ϡ�"+" ���ղä��롣</td></tr>
      <tr class="even"><td>����,select,����1,����2,����3</td><td>���� <select name='samp6'><option>����1</option><option>����2</option><option>����3</option></select></td><td></td></tr>
      <tr class="odd"><td>��٥�,const,��</td><td><em>��٥� ��</em></td><td>�����ͤȤ���ɽ�����졢���줬�����ͤȤʤ롣</td></tr>
      <tr class="even"><td>��٥�,hidden,��</td><td><em>(�ե�����ˤ�ɽ������ʤ�)</em></td><td>�����ͤȤ��Ƥ�ɽ������롣</td></tr>
      <tr class="odd"><td>#������ʸ</td><td>������ʸ</td><td>�ե�������ɽ������뤬�����ͤˤϤʤ�ʤ�</td></tr>
    </table>
  </form>
  </blockquote>

  <a name="custom"></a><h5>��������ե�����</h5>

  <p>������ʸ�ΰ����פ�ƥ�ץ졼�ȤȤ��ƻ��ꤹ��ȡ�����ʸ��ե���
  ��Υƥ�ץ졼�ȤȤ��ư������ƥ�ץ졼����ˤϡ��ե��������Ǥ�
  �ѿ� <tt>{̾��}</tt> �Ȥ��������ळ�Ȥ��Ǥ��롣</p>

  <blockquote>
    <table border="0" cellspacing="1" cellpadding="4" class="outer">
      <tr class="head"><th>�ѿ�̾</th><th>����</th></tr>
      <tr class="even"><td>{FORM_ATTR}</td><td>�ե����ॿ��°��</td></tr>
      <tr class="odd"><td>{SUBMIT}</td><td>�����ܥ���</td></tr>
      <tr class="even"><td>{BACK}</td><td>���Խ��ܥ���</td></tr>
      <tr class="odd"><td>{CHECK_SCRIPT}</td><td>�����Ѥ� JavaScript</td></tr>
      <tr class="even"><td>{����̾}</td><td>�ե��������Ǥ�̾��</td></tr>
      <tr class="odd"><td>{TO_UNAME} {TO_NAME}</td><td>�桼������Υե�����ˤ�����������桼��̾</td></tr>
    </table>
  <ul>
  <li>���ѿ���ɬ��1������и����뤳��</li>
  <li>�ե�����������̤ǡ֥ƥ�ץ졼�Ȥ��ɲáץܥ���ǻȤä��ѿ��켰�������Ǥ��롣</li>
  <li>�ե��������Τϼ��Τ褦�ʹ�����Ȥ� (���Ǥ� form ��������¦��Ǥ�դ����ֲ�ǽ)</li>
    <pre>&lt;form {FORM_ATTR}&gt;
����1: {����1}
����2*: {����2}
  :
����n: {����n}
{SUBMIT} {BACK}
&lt;/form&gt;
{CHECK_SCRIPT}</pre>
  <li><tt>[desc]����ʸ[/desc]</tt> �Ȥ���ȡ�
  ������ʸ�פϥե�����Ǥ���ɽ�������ǧ���̤Ǥ�ɽ������ʤ���</li>
  <li>{TO_UNAME}, {TO_NAME} �ϡ��̾�Υե����������ʸ��Ǥ�ͭ��</li>
  <li>�����Υƥ�ץ졼�ȡפ���ꤹ��� XOOPS �Υơ��ޤϻ��Ѥ��줺������ HTML �ˤ��ɽ���Ȥʤ롣</li>
  <li>������ƥ�ץ졼�ȡפ���ꤹ��ȥ֥�å���̵��������ư��롣����ʬ�ƥ�ץ졼�ȡפȤΰ㤤�ϡ�smarty �� <tt>$xoops_show{c,l,r}block</tt> �ѿ��� 0 �˻��ꤹ�뤳�ȤʤΤǡ��ƥ�ץ졼�Ȥ��������ѿ����θ���Ƥ���ɬ�פ����롣</li>
  </ul>
  </blockquote>

  <a name="config"></a><h5>��������</h5>
  <p>�⥸�塼��ΰ�������Ǥϰʲ��ι��ܤ����Ǥ��롣</p>

  <blockquote>
    <table border="0" cellspacing="1" cellpadding="4" class="outer">
      <tr class="head"><th>����̾</th><th>����/������</th><th>����</th></tr>
      <tr class="even"><td class="head">����ɽ���ο�</td><td><u>25</u></td><td>����ɽ����ɽ������Կ�����ꤹ��</td></tr>
      <tr class="odd"><td class="head">°���δ�����</td><td>size=60, rows=5, cols=50, notify_with_email=0</td><td>�ե���������Ǥ˻��ꤹ��°���δ����ͤʤɤ����ꤹ�롣</td></tr>
      <tr class="even"><td class="head">�����������</td><td><pre>����: - a b c
����Ԥ�: - a
��ȺѤ�: b c
--------:
������: -
�����: a
������: b
��λ: c</pre></td><td>�񼰤� ɽ��̾: [����1[,����2...]] ��ʣ���Ի��ꤹ�롣���֤� (-,a,b,c) ��ʸ���ǻ��ꤹ�롣
	  <p>
	    ���줾��ξ��֤ΰ�̣�ϡ�-:�����Ԥ�, a:�����,
	    b: �����Ѥ�, c: ��λ �Ȥʤ롣
	  </p>
      </td></tr>
    </table>

    <a name="attr"></a><h5>���ץ�����ѿ�</h5>

    <table border="0" cellspacing="1" cellpadding="4" class="outer">
      <tr class="head"><th>°��̾</th><th>����/������</th><th>����</th></tr>
      <tr class="even"><td class="head">size</td><td><u>0</u></td><td>�ƥ��������Ϲ��� (input) �������ΰ���</td></tr>
      <tr class="odd"><td class="head">maxlength</td><td><u>0</u></td><td>�ƥ��������Ϲ��� (input) �����Ͼ��ʸ����</td></tr>
      <tr class="even"><td class="head">rows</td><td><u>0</u></td><td>�ƥ����ȥ��ꥢ�ιԿ�</td></tr>
      <tr class="odd"><td class="head">cols</td><td><u>0</u></td><td>�ƥ����ȥ��ꥢ�η��</td></tr>
      <tr class="even"><td class="head">notify_with_email</td><td><u>0</u></td><td>1�����ꤷ������礻���Υ᡼��˥᡼�륢�ɥ쥹��ɽ������</td></tr>
      <tr class="odd"><td class="head">export_charset</td><td><u>UTF-8</u></td><td>CSV �ե�����ؽ��Ϥ�����Υ��󥳡��ǥ��󥰤���ꤹ�롣</td></tr>
      <tr class="even"><td class="head">redirect</td><td>�ʤ�</td><td>�ե����������������ܤ���ڡ����� URL����ꤹ��</td></tr>
      <tr class="odd"><td class="head">use_fckeditor</td><td>�ʤ�</td><td>�������Խ��� FCKeditor �Ȥ��������ͤ� Basic �ޤ��� Default</td></tr>
      <tr class="even"><td class="head">¾��̾��</td><td>����ɽ��</td><td>���ϸ�����ʸ�������
	  <p>��: ���� <tt>numeric=[-+]?[0-9]+</tt>�������ֹ�<tt>tel="\+?[0-9][0-9-,]*[0-9]"</tt><br />���Ѥϡ����Ϥ������ <tt>check=tel</tt> �Τ褦�����Ѥ��롣</p>
	  <p>����ɽ���ϡ�JavaScript �ǻȤ����ϰϤΤ�Τ�Ȥ����ȡ�(������¦�� perl ������ɽ�����Ȥ��������饤�����¦�� JavaScript �����Ѥ��뤿��)</p>
      </td></tr>
    </table>
  <blockquote>

  <a name="tips"></a><h5>�Ȥ������</h5>

  <h6>��礻������</h6>
  <p>
    ��礻�����Τϡ�XOOPS�Υ��٥�����Τ�����ˤ������äƹԤ��롣
    ��������ô���Ԥ�ô�����롼�פؤ����Τϡ��ä������Ԥ�ʤ�����Ԥ��롣
  </p>
  <ul>
    <li>���٥�����Τ���ˡ (�᡼��/PM/���) �ϥ桼��������˽���</li>
    <li>ô���Ԥ����ꤵ��Ƥ��ʤ���硢ô�����롼�פ����Τ��Ԥ���</li>
    <li>��礻��ô���Ԥˤʤä��桼���ˤϡ�������礻�ؤΥ��������Τ����ꤵ���</li>
    <li>ô���ԥ��롼�פΥ��С��ϥե�����������Τ�����Ǥ���</li>
    <li>�������¤������硢��礻�����Τ����Τ�����Ǥ���</li>
  </ul>

  <h6>����ϳ���к�</h6>
  <p>XOOPS �Υ����ȥ����ƥ�����Ѥ��빽¤�塢�����Ȥ���Ū��ɽ������褦�ʥ֥�å����⥸�塼��ΰ����ˤ���դ��뤳�ȡ�</p>
  <p>
    �㤨�С��֥����ƥ�����פΡֺǶ�Υ����ȡץ֥�å������Ѥ�����硢
    ��礻�ǤΥ����ȤΥ����ȥ뤬ɽ������뤳�Ȥˤʤ롣
    ���ξ�硢ɽ���ڡ����ǥ����������¤����Τ���ʸ���ɤळ�ȤϤǤ��ʤ�����
    ���٤��å������λ��֤�ϳ��Ƥ��ޤ���
  </p>
  <p>
    ���뤤�ϡ�Whatsnew(�������) �⥸�塼��ǡ���������ʸ��ɽ�������
    ���Ƥ�ϳ��Ƥ��ޤ���
  </p>

  <h6>���л���ˤ��ե�����</h6>
  <p>����ô���ԤȤ��ơ�����[���롼��] �ǻ��ꤷ���ե�����ϡ�
    HTTP������ uid ����ꤹ�뤳�Ȥ�ô���Ԥ���ꤷ���ե�����������
    �Ԥ����Ȥ��Ǥ��롣</p>

  <pre>XOOPS_URL/modules/ccenter/index.php?form=XX<b>&amp;uid=YY</b></pre>

  <p>
    ��������ϡ��̥ڡ�������嵭�����Υ�󥯤�ĥ�뤳�Ȥ����Ѥ���
    ���Ȥ����ꤷ�Ƥ��ꡢ���Υ⥸�塼��ñ�ȤǻȤ����ȤϤʤ���
  </p>

  <p>
    ŵ��Ū�ˤϡ֤��뵭�������Ԥ��Ф�����礻�ե�����פȸ������
    �ǡ��ƥ�ץ졼�Ȥ򥫥����ޥ������ƾ嵭��󥯤������ळ�Ȥ����Ѥ��롣
  </p>

  <a name="changes"></a><h5>�ѹ�����</h5>
  <dl>
  <dt>2011-03-15 ccenter 0.94</dt>
  <dd>
    <ul>
      <li>����ϳ�������ȼ������� (thx Hosiryuhosi)</li>
      <li>altsys �θ�������Ǥξ㳲���б�</li>
      <li>�ե����̾���ѹ����������Զ�����</li>
      <li>�����᡼��Υƥ�ץ졼�ȥե�����򥪥ץ�����ѿ����ѹ���ǽ�ˤ���</li>
    </ul>
  </dd>
  <dt>2009-11-15 ccenter 0.93</dt>
  <dd>
    <ul>
      <li>���Υ᡼����ɲå�����/�ƥ�ץ졼�Ȥ�ե�������˥��ץ�����ѿ��ǻ���</li>
      <li>���ץ�����ѿ��μ谷���������ѹ�</li>
    </ul>
  </dd>
  <dt>2009-07-04 ccenter 0.92</dt>
  <dd>
    <ul>
      <li>��礻�����˴ʰ׸������ɲ�</li>
      <li>�ե�����֥�å����Խ��ǽ���ͤ˵������ȿ�Ǥ��� (thx jun)</li>
      <li>�ɥ��ĸ�θ���꥽�������ɲ� (thx Rene)</li>
      <li>�ѥ󤯤��ꥹ�Ȥν�ü���󥯤ˤ��ʤ�</li>
      <li>��������ե�����Ǥ�ѥ󤯤��ꥹ�Ȥ������Ԥ�</li>
      <li>�ƥ�ץ졼�Ȥθ��ˤ���ư����� (thx L2)</li>
    </ul>
  </dd>
  <dt>2009-06-12 ccenter 0.91</dt>
  <dd>
    <ul>
      <li>����ɽ���� radio, select, checkbox ��ɽ��ʸ����ǹԤ�</li>
      <li>���Υƥ�ץ졼�Ȥ�ɽ����Ư���Ƥʤ��ä���Τ���</li>
      <li>�ե�����ƥ�ץ졼�Ȥδ�����󥯤δְ㤤����</li>
      <li>ź�եե����� (file) �Υƥ�ץ졼�Ȥθ����� (thx yata)</li>
      <li>�᡼�륢�ɥ쥹�κ���Ĺ��256�˳�ĥ (thx shige-p)</li>
    </ul>
  </dd>
  <dt>2009-06-06 ccenter 0.90</dt>
  <dd>
    <ul>
      <li>XOOPS 2.3 �ߴ��Τ���ν���</li>
      <li>�ե�������Υ��ץ����ξ�񤭤��ǽ�ˤ���</li>
      <li>�᡼�륢�ɥ쥹����ɽ���ˤʤ�ʤ��㳲����</li>
      <li>�ե��������ʤ�ƥ�ץ졼�Ȥ���������褦���ѹ�</li>
      <li>��礻������ɽ�����������֤ˤʤäƤ�����Τ��� (thx zorro87)</li>
      <li>1������ѥ���ɤǥ��󥳡��ɤˤ��ǧ�ڼ��Ԥ��ɤ����� (thx zorro87)</li>
      <li>�ե�����֥�å�������������θ����� (thx uhouho)</li>
      <li>�ݥ�ȥ����θ���꥽�������ɲ� (thx leco1)</li>
    </ul>
  </dd>
  <dt>2009-02-12 ccenter 0.89</dt>
  <dd>
    <ul>
      <li>��°���δ����͡פν񼰲��Ϥ�����ѹ�</li>
      <li>���٥�����ε�ǽ����礻���Τ���ǽ���Ƥ��ʤ��㳲���� (thx yue178)</li>
      <li>���Ϥ����ξ��ʸ���󸡺����������Ԥ��ʤ��㳲����</li>
      <li>�Ѹ����礻��ǧ�Υ᡼��ƥ�ץ졼�Ȥν���</li>
      <li>�����Υ桼�����ѿ���ɾ������</li>
      <li>��å�����ɽ�����̤Υƥ�ץ졼�Ƚ���</li>
    </ul>
  </dd>
  <dt>2008-06-15 ccenter 0.88</dt>
  <dd>
    <ul>
      <li>�����إ�����������(���ȥޡ���)��ɽ�����ɲ�</li>
      <li>�����������֤�ɽ������</li>
      <li>�����������֤ι�������ȿ�Ǥ���褦���ѹ�</li>
      <li>JavaScript ������ɽ�����������פ����</li>
      <li>CSV ���ϻ��Υ�٥뤫�� HTML ���������</li>
      <li>CSV ���Ϥ�ʸ�������ɻ����°�����ɲ� (export_charset=UTF-8)</li>
      <li>notify_with_email=1 ����¸�ǡ����� email ���ޤޤʤ��褦�˽���</li>
      <li>textarea ��ɬ�ܸ����ǲ��Ԥ���ȥ��顼�ˤʤ�㳲����</li>
    </ul>
  </dd>
  <dt>2008-06-01 ccenter 0.87</dt>
  <dd>
    <ul>
      <li>�ƥ�ץ졼�ȥ⡼�ɤǤ⥳���Ȥ�ɽ������</li>
      <li>°��̾��Ȥä����ϸ���������ǽ�ˤ���</li>
      <li>�ե�����񼰤� CSV �����ǰ�����褦�˸�̩��</li>
      <li>�֥�å��Υե�������������ܥ�����ѹ�</li>
      <li>�����ԥץ�ӥ塼�����</li>
      <li>��¸�ʤ��ξ���������ڡ����θ�����</li>
      <li>��礻�ԤΥ����������֤�Ͽ���� (��꤬�ɤ�����Ȥ��Τ뤿��)</li>
    </ul>
  </dd>
  <dt>2008-05-17 ccenter 0.86</dt>
  <dd>
    <ul>
      <li>�ǡ����١�������¸�⡼�ɡֵ�Ͽ���ʤ��פ��ɲ�</li>
      <li>Ƴ�����˥���ץ�ե�������������</li>
      <li>�ե�����°���δ����� notify_with_email=1 �����ꤹ��ȥ᡼�륢�ɥ쥹��ɽ������</li>
      <li>����̾����Ƭ�� "-" �ˤ���������ܤ�Ʊ���Ԥ�ɽ������</li>
    </ul>
  </dd>
  <dt>2008-02-29 ccenter 0.85</dt>
  <dd>
    <ul>
      <li>�ե���������Ѥ����ϥإ�Ѥ��ɲ�</li>
      <li>��λ���˥��������Τ�ȯ������褦�ˤ���</li>
      <li>��λ�����ξ���̾�Τ��ְ�äƤ�����Τ���</li>
      <li>CSV���Ϥ���ǯ���ϰϤ���äƤ�����Τ���</li>
    </ul>
  </dd>
  <dt>2008-01-31 ccenter 0.84</dt>
  <dd>
    <ul>
      <li>�Ѹ�꥽�������ɲ�</li>
      <li>����̾�����إ�٥���ĥ</li>
      <li>���ܷ������ (const) ���ɲ�</li>
      <li>�ե�����֥�å����ɲ�</li>
      <li>��������(')�����ΥХ�����</li>
      <li>IE �Ǥ� JavaScript�������Զ�����</li>
    </ul>
  </dd>
  <dt>2007-11-01 ccenter 0.83</dt>
  <dd>
    <ul>
      <li>CSV���Ϥ˴��ֻ�����ɲ�</li>
      <li>ô���ԥ��롼�פλ��꤬Ư���褦�˽���</li>
      <li>ɾ����λ��������/��Ͽ��Ԥ��褦�˽���</li>
    </ul>
  </dd>
  <dt>2007-10-27 ccenter 0.82</dt>
  <dd>
    <ul>
      <li>{REMOTE_ADDR}��{HTTP_USER_AGENT}��᡼���ѿ������</li>
      <li>������ƥ�ץ졼�ȡץ⡼�ɤ��ɲ�</li>
      <li>�����ȤΥ����Ⱦ㳲�ȥ���������Ͽ����</li>
      <li>�ѿ������ ({X_UNAME}=�桼��̾�ʤ�) �λ�������</li>
      <li>�������̤Ǥΰ���ѹ��Ǽ����Ԥ��ˤǤ��ʤ��㳲����</li>
      <li>�������̤Ǥξ����ѹ������顼�ˤʤ�㳲����</li>
    </ul>
  </dd>
  <dt>2007-08-06 ccenter 0.81</dt>
  <dd>
    <ul>
      <li>�ѥ󤯤�����������</li>
      <li>XCL 2.1 �Ѥ� blocks �Ǥ� global ������ɲ�</li>
    </ul>
  </dd>
  <dt>2007-08-03 ccenter 0.8</dt>
  <dd>
    <ul>
      <li>�����ѹ������Τ򥤥٥�����Τ��ѹ�</li>
      <li>��礻�����ڡ������ɲ�</li>
      <li>ô���Ԥ�������ˡ�����</li>
      <li>�ꥹ��ɽ�������Ѳ�</li>
      <li>�֥�å�ɽ���ξ���ĥ</li>
      <li>�ڡ��������ȥ�����</li>
      <li>ô����/��礻�Υڡ�����ʬΥ�ȶ���</li>
      <li>��å��������ʤ��Ȥ����եڡ����˥��������Ǥ��ʤ��㳲����</li>
      <li>�ƺ��ѥơ��֥� (log) ���ɲ�</li>
      <li>altsys ����������Ѥ���</li>
    </ul>
  </dd>
  <dt>2007-06-14 ccenter 0.71</dt>
  <dd>
    <ul>
      <li>°���δ����ͤ�����ǽ�ˤ���</li>
      <li>�㳲����: �����ȤΥ�ץ饤��ƻ��˥����Ȥ����Τ��Ԥ��ʤ�</li>
      <li>�㳲����: �����Υե�����̾���ϥ�ɥ�󥰤Ǥ��ʤ�</li>
      <li>�㳲����: hilien �� mail ���Ȥ߹�碌��Ȱ۾郎������</li>
      <li>�㳲����: radio �ǡ��֤���¾�׵�ǽ��Ȥ��ȡ��ǥե���Ȥ�ͭ���ˤʤ�</li>
    </ul>
  </dd>
  <dt>2007-05-13 ccenter 0.7</dt>
  <dd>
    <ul>
      <li>ô���Ԥ�ưŪ��Ǥ�ե桼���ˤ���</li>
      <li>CSV ���Ϥ� SJIS ���� UTF-8 ���ѹ�</li>
      <li>������֤λ���Ǥ��ʤ��Τ���</li>
      <li>checkbox, radio �ˡ�����¾ [�ƥ�����] ��Ȥ���褦�ˤ���</li>
      <li>�����ΰ���Ū�γ�ĥ (check=xx)</li>
      <li>hilien �����ƥ�μ���</li>
      <li>�Х�����: �����Ȥ�ɽ�����֤���������</li>
    </ul>
  </dd>
  <dt>2007-03-07 ccenter 0.6</dt>
  </dl>

<hr/>
<aliress>���� ���� &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</aliress>
�ޥ������� �桼������ <a href="http://myht.org/">http://myht.org/</a><br/>
$Id: help.html,v 1.5 2011-03-15 13:53:02 nobu Exp $
</body>
</html>
